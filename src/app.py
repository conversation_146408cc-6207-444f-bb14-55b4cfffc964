from flask import Flask
from flask import render_template
from flask import request
import os
from engine import ChessAnalysisPool
from utils import pgn2board, pgn2uci, sample_move, uci2board
from dotenv import load_dotenv
import chess
from utils import previous_move_and_uci
from explorer import Explorer
from judge import Judge

load_dotenv()


def create_app():
    app = Flask(__name__)

    app.pool = ChessAnalysisPool(
        book_path=os.environ.get("BOOK_PATH"),
        num_workers=2,
    )
    app.explorer = Explorer(os.environ.get("EXPLORER_CACHE_PATH"), num_workers=2)
    app.judge = Judge(app.pool)
    app.MIN_OCCURRENCES = 10
    app.SAMPLE_THRESHOLD = 0.05
    app.MULTI_PV = 3

    return app


app = create_app()


def finish_game(hint_move: str = "", fen_before_wrong_move: str = "") -> dict:
    return {
        "best_move": hint_move,
        "continue": False,
        "fen_before_wrong_move": fen_before_wrong_move,
    }


def continue_game(
    uci: str, training_move: int, engine_type: str, move_limit: int
) -> dict:
    # Don't judge the first move
    if training_move > 1:
        correct_move, best_moves = app.judge.last_move_is_correct(
            uci, engine_type, move_limit, app.MULTI_PV
        )
        if not correct_move:
            _, prev_uci = previous_move_and_uci(uci)
            prev_board = uci2board(prev_uci)
            fen_before_wrong_move = prev_board.fen()
            return finish_game(
                hint_move=" ".join(best_moves),
                fen_before_wrong_move=fen_before_wrong_move,
            )
    else:
        correct_move = True

    moves_dst = app.explorer.submit_and_get(uci)
    if len(moves_dst) == 0:
        print("No moves found, finishing game.")
        return finish_game()

    move, occurrences = sample_move(moves_dst, threshold=app.SAMPLE_THRESHOLD)
    print(f"🧭 Explorer move: {move}, occurrences: {occurrences}")
    if occurrences < app.MIN_OCCURRENCES:
        print("Not enough occurrences, finishing game.")
        return finish_game()

    app.pool.submit_job(f"{uci} {move}", engine_type, move_limit, app.MULTI_PV)

    board = uci2board(uci)
    board.push(chess.Move.from_uci(move))

    return {
        "best_move": str(move),
        "continue": correct_move,
    }


# root(index) route
@app.route("/")
def root():
    return render_template("bbc.html")


# make move API
@app.route("/make_move", methods=["POST"])
def make_move():
    # extract FEN string from HTTP POST request body
    pgn = request.form.get("pgn")
    board = pgn2board(pgn)
    uci = pgn2uci(pgn)

    # extract move time value
    move_time = request.form.get("move_time")
    engine_type = request.form.get("engine_type", "stockfish")
    training_move = int(request.form.get("training_move", 0))

    # if move time is available
    move_limit = 0.1 if move_time == "instant" else int(move_time)

    orientation = (
        chess.WHITE if request.form.get("orientation") == "white" else chess.BLACK
    )
    players_turn = orientation == board.turn

    if players_turn:
        return {"best_move": "", "continue": False}
    else:
        return continue_game(uci, training_move, engine_type, move_limit)


# main driver
if __name__ == "__main__":
    # start HTTP server
    app.run(host="0.0.0.0", debug=True, threaded=True)
